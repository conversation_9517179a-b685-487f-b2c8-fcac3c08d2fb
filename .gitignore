# Python virtual environment
myenv/
venv/
env/
.env/

# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/

# Logs
*.log

# Local configuration
.env
.env.local

# IDE specific files
.idea/
.vscode/

# File Uploads and Temporary Files (CRITICAL - Never push user files)
**/uploads/
**/outputs/
**/data/
**/temp/
**/tmp/
**/*.tmp
**/*.temp
**/processed_files/
**/user_uploads/
**/csv_outputs/
**/image_temp/
**/pdf_temp/

# API Keys and Secrets (CRITICAL - Never push these)
**/secrets/
**/keys/
**/*secret*
**/*key*
**/*password*
**/*token*
local.settings.json

# Azure Functions
**/.azure/
**/.azurefunctions/

# Node.js (for frontend)
**/node_modules/
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*
**/.npm
**/.eslintcache

# Certificate Files (CRITICAL)
**/*.pem
**/*.key
**/*.crt
**/*.p12
**/*.pfx

# Database Files
**/*.db
**/*.sqlite
**/*.sqlite3

# Test Files with Sensitive Data
**/test_data/
**/sample_files/
**/test_uploads/
*.swp
*.swo

# Additional Security - Harmful/Sensitive Files
**/*config*.json
**/*settings*.json
**/credentials/
**/auth/
**/*.env*
**/gemini_key*
**/api_key*
**/openai_key*
**/*_key.txt
**/*_secret.txt
**/*_token.txt
**/*_password.txt

# User Generated Content (Never push user files)
**/marksheet*
**/invoice*
**/document*
**/receipt*
**/scanned_*
**/scan_*
**/photo*
**/image*
**/*.jpg
**/*.jpeg
**/*.png
**/*.pdf
**/*.csv
**/*.xlsx
**/*.docx
**/*.txt
!**/README.txt
!**/requirements.txt
!**/package.json

# System Files
.DS_Store
Thumbs.db
desktop.ini

# Backup Files
**/*.bak
**/*.backup
**/*.old
